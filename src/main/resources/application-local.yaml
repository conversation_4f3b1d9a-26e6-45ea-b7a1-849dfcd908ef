spring:
  datasource:
    url: ****************************************?${mysqlArgs}
    username: dev
    password: devdev
    hikari:
      max-lifetime: 120000 # 由于代理的存在，数据库连接idle10分钟可能就失效了，远没有到mysql的8小时
  data:
    redis:
      host: *************
      port: 6379
      password: devdev
      database: 6

admin:
  securityLevel: NONE
  isActuallySendMsg: false
  enableWebLog: false # TODO 这里需要admin处理下之后才能打开，admin得支持排除在外的url

question:
  editorHost: http://*************:31680