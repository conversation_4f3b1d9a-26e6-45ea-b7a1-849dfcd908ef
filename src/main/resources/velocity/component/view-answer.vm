<!--
作用：负责查看答案，要点是editormd的iframe嵌入
入参：
question 对应于后端的question对象，主要用到answerHtml属性
事件：无
 -->

<style>
    .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        padding-bottom: 0px;
        color: gray
    }
</style>

<script type="text/x-template" id="viewAnswer">
    <div style="height: 600px">
        <iframe ref="iframe" src="/editormd/view.html"
              style="border: none; width: 840px; height: 500px; transform: scale(1.1);transform-origin: 0 0;"
              @load="handleIframeLoad"></iframe> <!-- 宽度840px是小屏的最大值了 -->
    </div>
</script>

<script>
    Vue.component('view-answer', {
        template: '#viewAnswer',
        props: {
            question: {
                type: Object,
                required: true
            }
        },
        methods: {
            handleIframeLoad() {
                const iframe = this.$refs.iframe;
                if (iframe && iframe.contentWindow && typeof iframe.contentWindow.setContent === 'function') {
                    iframe.contentWindow.setContent(this.question.answerHtml);
                }
            }
        }
    })
</script>