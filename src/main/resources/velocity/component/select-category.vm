<!--
作用：分类树型结构展示，用于选择分类，不提供修改功能
入参：
categoryId 分类父节点id，默认为0，即根节点

事件：
select-category 当树节点选择时触发，传递参数：String selectCategoryId, String selectCategoryName
-->

<style>
</style>

<script type="text/x-template" id="selectCategory">
    <div>
        <div>
            <el-input v-model="treeFilterText" placeholder="过滤分类节点" style="width: 120px" size="small"></el-input>
        </div>
        <el-tree :data="treeData" node-key="id" highlight-current ref="tree" :props="treeProps"
                 default-expand-all :expand-on-click-node="false"
                 class="filter-tree" :filter-node-method="filterNode">
              <span class="custom-tree-node" slot-scope="{node,data}">
                <el-radio :label="data.id" v-model="selectedCategoryId" @input="selected(data.id,node.label)">{{node.label}}</el-radio>
              </span>
        </el-tree>
    </div>
</script>

<script>
    Vue.component('select-category', {
        template: '#selectCategory',
        props: {
            categoryId: {
                type: String,
                required: true
            }
        },
        data: function() {
            return {
                treeData: [],
                treeProps: {children: 'children',label: 'categoryName'}, /*自定义树的变量名称*/
                treeFilterText: '',
                selectedCategoryId: ''
            }
        },
        created: function() {
            this.getCategoryTree()
        },
        watch: {
            treeFilterText: function (val) {this.$refs.tree.filter(val)},
        },
        methods: {
            getCategoryTree: function() {
                var that = this
                Resource.get("${_contextPath_}/question_category/query_tree", {
                    categoryId: that.categoryId
                }, function(resp){
                    that.treeData = resp.data.tree
                })
            },
            filterNode: function (value, data) {
                if (!value) return true;
                var { pinyin } = pinyinPro;
                var convertedText = pinyin(data.categoryName, {
                    type: 'text',
                    toneType: 'none',    // 不显示音调
                    segment: true, // 启用分词，智能处理多音字
                    separator: '', // 不要空格
                    nonZh: 'original' // 保留非中文字符
                });
                return data.categoryName.toLowerCase().indexOf(value.toLowerCase()) !== -1
                        || convertedText.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
            selected: function (selectCategoryId, selectCategoryName) {
                this.$emit('select-category', selectCategoryId, selectCategoryName)
            }
        }
    })
</script>