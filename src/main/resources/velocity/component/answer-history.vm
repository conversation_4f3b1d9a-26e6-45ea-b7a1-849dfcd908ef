<!--
作用：负责显示问题的历史 TODO 未实现
入参：
question 对应于后端的question对象
事件：无
 -->

<style>
</style>

<script type="text/x-template" id="answerHistory">
    <div>
        <el-descriptions title="" border :column="2">
            <el-descriptions-item label="创建时间">{{question.createTime}}</el-descriptions-item>
            <el-descriptions-item label="修改时间">{{question.updateTime}}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{question.creatorName}}</el-descriptions-item>
            <el-descriptions-item label="修改人">{{question.updaterName}}</el-descriptions-item>
        </el-descriptions>
    </div>
</script>

<script>
    Vue.component('answer-history', {
        template: '#answerHistory',
        props: {
            question: {
                type: Object,
                required: true
            }
        },
        methods: {
        }
    })
</script>