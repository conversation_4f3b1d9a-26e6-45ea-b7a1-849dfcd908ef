<!--
作用：分类树型结构展示、增删改
入参：
categoryId 分类父节点id，默认为0，即根节点

事件：
category-check-change 当树节点勾选或取消勾选时，传递参数：String treeCheckedIds
-->

<style>
    .custom-tree-node {flex: 1;display: flex;align-items: center;justify-content: space-between;font-size: 16px;padding-right: 28px;
        height:100%;/*修复官方slot不是100%高导致点击编辑偶发没反应的问题 TODO 待试试真实下有没有问题*/}
</style>

<script type="text/x-template" id="categoryTree">
    <div>
        <div>
            <el-button @click="appendNode(null, null)" size="small" v-if="categoryId=='0'">新增顶级节点</el-button>
            <el-input v-model="treeFilterText" placeholder="过滤分类节点" style="width: 120px" size="small"></el-input>
        </div>
        <el-tree :data="treeData" node-key="id" highlight-current ref="tree" :props="treeProps"
                 default-expand-all :expand-on-click-node="false"
                 class="filter-tree" :filter-node-method="filterNode"
                 draggable @node-drop="saveTreeNode" :allow-drag="allowDrag" :allow-drop="allowDrop"
                 show-checkbox check-on-click-node @check="treeCheckBoxChanged">
              <span class="custom-tree-node" slot-scope="{node,data}">
                <span>{{node.label}}<span v-if="data.questionCount !== undefined" style="color: #909399; margin-left: 5px;">({{data.questionCount}})</span></span>
                <span>
                  <i class="el-icon-circle-plus-outline" @click.stop="appendNode(data, node)"></i>
                  <i class="el-icon-edit" @click.stop="editNode(data, node)"></i>
                </span>
              </span>
        </el-tree>

        <el-dialog :title="treeDialogTitle" :visible.sync="treeShowDialog" top="10px" :close-on-click-modal="false" :append-to-body='true'>
            <el-form :model="treeAddEditForm" label-position="right" label-width="150px">
                <el-form-item label="分类名称" prop="categoryName">
                    <el-input v-model="treeAddEditForm.categoryName" placeholder="分类名称"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="danger" @click="handleTreeDelete" v-show="treeAddEditForm.id">删除</el-button>
                <el-button @click="treeShowDialog = false">取消</el-button>
                <el-button type="primary" @click="doTreeAddOrEdit">确定</el-button>
            </div>
        </el-dialog>
    </div>
</script>

<script>
    Vue.component('category-tree', {
        template: '#categoryTree',
        props: {
            categoryId: {
                type: String,
                required: true
            }
        },
        data: function() {
            return {
                treeData: [],
                treeProps: {children: 'children',label: 'categoryName'}, /*自定义树的变量名称*/
                treeFilterText: '',
                treeAddEditForm: {},
                treeIsAdd: true,
                treeShowDialog: false,
                treeDialogTitle: '',
                treeAddEditNode: {},
                treeCheckedIds: ''
            }
        },
        created: function() {
            this.getCategoryTree()
        },
        watch: {
            treeFilterText: function (val) {this.$refs.tree.filter(val)}
        },
        methods: {
            getCategoryTree: function() {
                var that = this
                Resource.get("${_contextPath_}/question_category/query_tree", {
                    categoryId: that.categoryId
                }, function(resp){
                    that.treeData = resp.data.tree
                })
            },
            filterNode: function (value, data) {
                if (!value) return true;
                var { pinyin } = pinyinPro;
                var convertedText = pinyin(data.categoryName, {
                    type: 'text',
                    toneType: 'none',    // 不显示音调
                    segment: true, // 启用分词，智能处理多音字
                    separator: '', // 不要空格
                    nonZh: 'original' // 保留非中文字符
                });
                return data.categoryName.toLowerCase().indexOf(value.toLowerCase()) !== -1
                        || convertedText.toLowerCase().indexOf(value.toLowerCase()) !== -1;
            },
            saveTreeNode: function() {
                var that = this
                Resource.postJson("${_contextPath_}/question_category/save_tree", {
                    categoryId: that.categoryId || "0",
                    tree: this.treeData
                }, function(resp){
                    Message.success("分类保存成功")
                    that.getCategoryTree()
                })
            },
            appendNode: function(data, node) {
                this.treeDialogTitle = '新增节点到: ' + (data == null ? '根目录' : data.categoryName)
                this.treeAddEditForm = {}
                this.treeShowDialog = true
                this.treeIsAdd = true
                this.treeAddEditNode = data == null ? this.treeData : data // 根节点是个数组
            },
            editNode: function (data, node) {
                this.treeDialogTitle = '编辑节点'
                this.treeAddEditForm = Utils.copy(data)
                this.treeShowDialog = true
                this.treeIsAdd = false
                this.treeAddEditNode = data
            },
            doTreeAddOrEdit: function() {
                if (this.treeIsAdd) {
                    if (Array.isArray(this.treeAddEditNode)) { // 根节点是个数组
                        this.treeAddEditNode.push(this.treeAddEditForm)
                    } else {
                        if (!this.treeAddEditNode.children) {
                            this.$set(this.treeAddEditNode, 'children', [])
                        }
                        this.treeAddEditNode.children.push(this.treeAddEditForm)
                    }
                } else {
                    this.$set(this.treeAddEditNode, 'categoryName', this.treeAddEditForm.categoryName)
                }

                this.saveTreeNode()
                this.treeShowDialog = false
            },
            handleTreeDelete: function() {
                function removeNodeByReference(tree, nodeToRemove) {
                    for (let i = 0; i < tree.length; i++) {
                        const node = tree[i];
                        if (node === nodeToRemove) {
                            tree.splice(i, 1);
                            return true; // 删除成功
                        } else if (node.children && node.children.length > 0) {
                            const removed = removeNodeByReference(node.children, nodeToRemove);
                            if (removed) return true;
                        }
                    }
                    return false; // 未找到该节点
                }

                var that = this
                Message.confirm("确定删除节点：" +  this.treeAddEditNode.categoryName + "及其所有子节点吗?",
                        function() {
                            if (removeNodeByReference(that.treeData, that.treeAddEditNode)) {
                                that.saveTreeNode()
                            }
                            that.treeShowDialog = false
                        })
            },
            treeCheckBoxChanged: function(checkedKeys, { checked, checkedNodes, node, event }) {
                const idArray = [];
                const nameArray = [];
                for (let i = 0; i < checkedNodes.length; i++) {
                    idArray.push(checkedNodes[i].id);
                    nameArray.push(checkedNodes[i].categoryName);
                }
                this.treeCheckedIds = idArray ? idArray.join(',') : '';
                this.treeCheckedNames = nameArray ? nameArray.join(',') : '';
                this.$emit('category-check-change', this.treeCheckedIds, this.treeCheckedNames)
            },
            allowDrag: function(node) {
                return String(node.data.id) !== this.categoryId
            },
            allowDrop: function(draggingNode, dropNode, type) {
                return String(dropNode.data.id) !== this.categoryId
            },
        }
    })
</script>