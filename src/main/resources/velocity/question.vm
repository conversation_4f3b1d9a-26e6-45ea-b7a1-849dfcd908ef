<!-- question.vm -->
#set($page_title='问题列表')

#parse("component/view-answer.vm")
#parse("component/category-tree.vm")
#parse("component/select-category.vm")
#parse("component/answer-history.vm")

<style>
    .table-row-expand label {width: 120px;color: #99a9bf;}
    .table-row-expand .el-form-item {width: 100%;}

    .editor-dialog .el-dialog__header {
        display: none;
    }
    .editor-dialog .el-dialog__body {
        padding: 5px 5px 5px 0px;
    }
    .editor-dialog .el-dialog__footer {
        padding: 0 5px 5px 5px;
    }

    .el-tag {cursor:hand}

    .compact-rate .el-rate__icon {
        margin-right: -4px !important; /* 调整为所需的间距 */
    }
</style>

<div id="app" v-cloak style="margin: 0">
    <el-container style="height: 100%; margin-left: 3px">
        <el-aside width="280px" style="padding-top:5px">
            <category-tree :category-id="categoryId" @category-check-change="treeCategoryCheckChange"></category-tree>
        </el-aside>
        <el-container>
          <el-main style="padding: 0">
            <el-form :inline="true" @keyup.native.enter="getData">
                <el-form-item label="">
                    <el-input v-model="queryForm.questionLike" placeholder="模糊搜索问题，关键词空格隔开" size="small"
                              style="margin-top: 5px; width: 250px"></el-input>
                </el-form-item>
                <el-form-item label="">
                    <el-select v-model="queryTags" multiple filterable placeholder="过滤标签" size="small" style="margin-top: 5px; width: 220px">
                        <el-option v-for="item in allTags" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-input style="display: none"></el-input> <!-- hidden el-input to make keyup search work when there is only one input -->
                <el-form-item>
                    <el-button type="primary" @click="(queryForm.page=1) && getData()" size="small">查询</el-button>
                    <el-button @click="resetQuery" size="small">重置</el-button>
                    <el-button type="success" @click="handleAddOrEdit(true)" size="small">新增</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="tableData" border stripe v-loading.body="tableLoading">
                <el-table-column type="expand" label="答案" width="80px">
                    <template slot-scope="props">
                        <view-answer :question="props.row"></view-answer>
                    </template>
                </el-table-column>
                <el-table-column prop="question" label="问题" min-width="400"></el-table-column>
                <el-table-column label="分类" min-width="70px">
                    <template slot-scope="scope">
                        <el-tooltip :content="scope.row.categoryFullPath || scope.row.categoryName" placement="top" :disabled="!scope.row.categoryFullPath">
                            <span>{{scope.row.categoryName}}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column prop="star" label="评分" width="100px">
                    <template slot-scope="scope">
                        <el-rate v-model="scope.row.star" disabled show-score text-color="#ff9900" score-template="" class="compact-rate" :colors="['#99A9BF', '#F7BA2A', '#FF9900']">
                        </el-rate>
                    </template>
                </el-table-column>
                <el-table-column label="标签" width="140px">
                    <template slot-scope="scope">
                        <el-tag v-for="tag in scope.row.tags" :key="tag" size="mini" @click="addQueryTag(tag)">{{tag}}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="131px">
                    <template slot-scope="scope">
                        <el-button-group>
                            <el-button type="primary" size="mini" @click="handleAddOrEdit(false, scope.row)"><i class="el-icon-edit"></i></el-button>
                            <el-button type="plan" size="mini" @click="showHistory(scope.row)"><i class="el-icon-time"></i></el-button>
                        </el-button-group>
                    </template>
                </el-table-column>

            </el-table>

            <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                           :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
            </el-pagination>
          </el-main>
        </el-container>
    </el-container>

    <el-dialog :title="dialogTitle" :visible.sync="showDialog" :close-on-click-modal="false" width="90%" top="5px" height="100%" class="editor-dialog" >
        <el-form :model="addEditForm" label-position="right" label-width="50px" :rules="rules" ref="addEditForm">
            <el-form-item label="问题" prop="question" required>
                <el-input v-model="addEditForm.question" placeholder="问题"></el-input>
            </el-form-item>
            <iframe src="/editormd/?sys=question" style="border: 0; width: 100%; height: 85%" id="editormd" onload="setEditormdContent()"></iframe>
        </el-form>
        <div slot="footer">
            <el-form :inline="true">
                <el-form-item label="分类" style="float: left" required>
                    <el-popover placement="top-start" title="" width="300" trigger="hover">
                        <select-category :category-id="categoryId" @select-category="selectCategory" v-if="showDialog"></select-category>
                        <el-button slot="reference" size="small">{{addEditForm.categoryName || '请选择'}}</el-button>
                    </el-popover>
                </el-form-item>
                <el-form-item label="评分" style="float: left; padding-left: 15px">
                    <el-rate v-model="addEditForm.star" show-score text-color="#ff9900" score-template="" class="compact-rate" :colors="['#99A9BF', '#F7BA2A', '#FF9900']" style="padding-top: 10px">
                    </el-rate>
                </el-form-item>
                <el-form-item label="标签" style="float: left; padding-left: 15px">
                    <el-select v-model="addEditForm.tags" multiple filterable allow-create placeholder="请选择标签，可以自行输入创建" size="small" style="width: 400px">
                        <el-option v-for="item in allTags" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <span>
                    <el-button type="danger" @click="handleDelete(addEditForm)" v-show="addEditForm.id" size="small">删除</el-button>
                    <el-button @click="cancelAddOrEdit" size="small">取消</el-button>
                    <el-button type="primary" @click="doAddOrEdit" size="small" :loading="saveLoading">保存</el-button>
                </span>
            </el-form>
        </div>
    </el-dialog>

    <el-dialog title="查看问题历史" :visible.sync="showHistoryDialog" top="5px" width="800px">
        <answer-history :question="currentQuestion"></answer-history>
        <div slot="footer">
            <el-button type="primary" @click="showHistoryDialog = false" size="small">确定</el-button>
        </div>
    </el-dialog>
</div>

<script src="${_contextPath_}/js/pinyin-pro.min.js"></script>
<script>
    var defaultQueryForm = {page: 1, pageSize: 10}
    var defaultAddForm = {}
    var vm = new Vue({
        el: '#app',
        data: {
            categoryId: Resource.getUrlParam('categoryId') || '0',
            queryForm: Utils.copy(defaultQueryForm),
            addEditForm: Utils.copy(defaultAddForm),
            rules: {question: Form.notBlankValidator('问题必填')},
            total: 0, tableData: [], tableLoading: false,
            showDialog: false, dialogTitle: '',
            showHistoryDialog: false, currentQuestion: {},
            isCloseDialogAfterEditIframeReload: false,
            treeCheckedIds: '', treeCheckedNames: '',
            allTags: [],
            queryTags: [],
            saveLoading: false
        },
        created: function() {
            this.queryForm.categoryId = this.categoryId
            this.getData()
            this.getAllTags()
            var that = this
            window.setEditormdContent = function() {
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    iframe.contentWindow.setEditormdContent(that.addEditForm.answer);
                }
                if (that.isCloseDialogAfterEditIframeReload) {
                    that.isCloseDialogAfterEditIframeReload = false
                    that.showDialog = false
                }
            }
        },
        methods: {
            getData: function() {
                var that = this
                that.tableLoading = true
                this.queryForm.treeCheckedIds = this.treeCheckedIds
                if (this.queryTags) {
                    this.queryForm.tags = this.queryTags.join(',')
                } else {
                    this.queryForm.tags = ''
                }
                Resource.get("${_contextPath_}/question/get_page", this.queryForm, function(resp){
                    that.tableData = resp.data.data
                    that.total = resp.data.total
                    that.tableLoading = false
                })
            },
            getAllTags: function() {
                var that = this
                Resource.get("${_contextPath_}/question/get_all_tags", {}, function(resp){
                    that.allTags = resp.data
                })
            },
            pageChange: function(page) {
                this.queryForm.page = page
                this.getData()
            },
            resetQuery: function() {
                this.queryForm = Utils.copy(defaultQueryForm)
                this.queryTags = []
                this.getData()
            },
            reloadEditIframe: function() {
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    iframe.contentWindow.location.reload();
                }
            },
            handleDelete: function(row) {
                var that = this
                Message.confirm("确定要删除吗?", function(){
                    Resource.post("${_contextPath_}/question/delete", {id: row.id}, function(){
                        that.showDialog = false
                        Message.success("删除成功，列表已刷新")
                        that.getData()
                    })
                })
            },
            handleAddOrEdit: function(isAdd, row) {
                this.dialogTitle = isAdd ? '新增问题' : '编辑问题'
                Form.clearError(this, 'addEditForm')

                // 先清空表单内容，避免显示上一次的内容
                this.addEditForm = Utils.copy(defaultAddForm)

                // 显示对话框
                this.showDialog = true

                // 然后设置正确的表单数据
                this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)

                // 如果是新增且当前有选中的单个分类ID，则自动设置该分类
                if (isAdd && this.treeCheckedIds) {
                    // 取this.treeCheckIds以,分隔的最后一个
                    this.addEditForm.categoryId = this.treeCheckedIds.split(',').pop()
                    this.addEditForm.categoryName = this.treeCheckedNames.split(',').pop()
                }

                this.reloadEditIframe()
            },
            doAddOrEdit: function() {
                var that = this
                var isEdit =  !!this.addEditForm.id
                var iframe = document.getElementById('editormd');
                if (iframe) {
                    that.addEditForm.answer = iframe.contentWindow.getEditormdContent()
                    that.addEditForm.answerHtml = iframe.contentWindow.getEditormdContentHtml()
                } else {
                    Message.error("无法获取editor的内容")
                    return;
                }
                Form.validate(this, 'addEditForm', function() {
                    that.saveLoading = true
                    Resource.postJson("${_contextPath_}/question/add_or_update", that.addEditForm, function(resp){
                        iframe.contentWindow.markSaved()
                        Message.success(isEdit ? "修改成功" : "新增成功，您可以继续添加问题或关闭")
                        isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
                        that.getData()
                        that.reloadEditIframe()
                        that.showDialog = false
                    }, null /*error callback use default*/, function() { // finally callback
                        that.saveLoading = false
                    })
                })
            },
            cancelAddOrEdit: function () {
                var that = this
                // 先判断一下内容是否有修改，如果没有修改，那么先隐藏调dialog，再做下面的清理工作，这样页面交互更好
                var iframe = document.getElementById('editormd');
                if (iframe && that.addEditForm.answer === iframe.contentWindow.getEditormdContent()) {
                    that.showDialog = false
                }

                this.addEditForm = Utils.copy(defaultAddForm)
                this.reloadEditIframe() // 调用这个是为了让浏览器触发提示用户记得保存内容的提示
                this.isCloseDialogAfterEditIframeReload = true
            },
            treeCategoryCheckChange: function(treeCheckedIds, treeCheckedNames) {
                this.treeCheckedIds = treeCheckedIds
                this.treeCheckedNames = treeCheckedNames
                this.getData()
            },
            selectCategory: function (selectCategoryId, selectCategoryName) {
                this.$set(this.addEditForm, 'categoryId', selectCategoryId)
                this.$set(this.addEditForm, 'categoryName', selectCategoryName)
            },
            addQueryTag: function(tag) {
                if (this.queryTags.indexOf(tag) === -1) {
                    this.queryTags.push(tag)
                    this.getData()
                }
            },
            showHistory: function (row) {
                this.showHistoryDialog = true
                this.currentQuestion = row
            }
        }
    })
</script>