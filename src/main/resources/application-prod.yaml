spring:
  datasource:
    url: ************************************?${mysqlArgs}
    username: idc
    password: idc
  data:
    redis:
      host: *********
      port: 6379
      password: Redis118229SZ
      database: 6

admin:
  securityLevel: LOOSE
  enableWebLog: false # TODO 这里需要admin处理下之后才能打开，admin得支持排除在外的url，这里应该是外部嵌入编辑器的原因，请求过多，记录很多
  notifyWhenRestart: true

question:
  editorHost: http://*********:31680