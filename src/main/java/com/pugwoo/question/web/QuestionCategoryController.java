package com.pugwoo.question.web;

import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.question.entity.vo.QuestionCategoryTreeNodeVO;
import com.pugwoo.question.service.QuestionCategoryService;
import com.pugwoo.question.web.req.SaveQuestionCategoryTreeReq;
import com.pugwoo.question.web.resp.QueryQuestionCategoryTreeResp;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/question_category")
public class QuestionCategoryController {

    @Resource
    private QuestionCategoryService questionCategoryService;

    @GetMapping("/query_tree")
    public QueryQuestionCategoryTreeResp queryQuestionCategoryTree(@RequestParam(value = "categoryId", required = false)
                                                                   Long categoryId) {
        List<QuestionCategoryTreeNodeVO> categoryTree = questionCategoryService.getCategoryTree(categoryId);
        QueryQuestionCategoryTreeResp resp = new QueryQuestionCategoryTreeResp();
        resp.setTree(categoryTree);
        return resp;
    }

    @PostMapping("/save_tree")
    public Boolean saveCategoryTree(@RequestBody SaveQuestionCategoryTreeReq req) {
        WebCheckUtils.assertNotNull(req, "参数不能为空");
        WebCheckUtils.assertNotNull(req.getTree(), "参数tree不能为空");
        WebCheckUtils.assertNotNull(req.getCategoryId(), "参数categoryIds不能为空");

        questionCategoryService.saveCategoryTree(req.getCategoryId(), req.getTree());
        return true;
    }

}
