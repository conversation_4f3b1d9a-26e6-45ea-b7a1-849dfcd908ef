package com.pugwoo.question.web;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.question.entity.vo.ForPageQuestionVO;
import com.pugwoo.question.service.QuestionService;
import com.pugwoo.question.utils.SomeUtils;
import com.pugwoo.question.web.req.SaveQuestionReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/question")
public class QuestionController {

    @Autowired
    private QuestionService questionService;
    
    @GetMapping("list")
    public ModelAndView list() {
        return new ModelAndView("question");
    }

    @GetMapping("get_page")
    public WebJsonBean<Map<String, Object>> getPage(int page, int pageSize,
                                                    @RequestParam(value = "questionLike", required = false) String questionLike,
                                                    @RequestParam(value = "categoryId", required = false) Long categoryId,
                                                    @RequestParam(value = "treeCheckedIds", required = false) String treeCheckedIds,
                                                    @RequestParam(value = "tags", required = false) String tags) {
        PageData<ForPageQuestionVO> pageData = questionService.getPage(page, pageSize, categoryId,
                SomeUtils.parseCategoryIds(treeCheckedIds), questionLike, SomeUtils.parseTags(tags));
        Map<String, Object> result = PageUtils.trans(pageData);
        return WebJsonBean.ok(result);
    }
    
    @PostMapping("add_or_update")
    public WebJsonBean<Long> addOrUpdate(@RequestBody SaveQuestionReq questionDO) {
        WebCheckUtils.assertNotNull(questionDO, "缺少修改的对象参数");
        WebCheckUtils.assertNotBlank(questionDO.getQuestion(), "缺少参数question，必须填写");
        WebCheckUtils.assertNotNull(questionDO.getCategoryId(), "请选择分类");

        ResultBean<Long> result = questionService.insertOrUpdate(questionDO, questionDO.getTags());
        return result.isSuccess() ? WebJsonBean.ok(result.getData()) : WebJsonBean.of(result);
    }
    
    @PostMapping("delete")
    public WebJsonBean<Boolean> delete(Long id) {
        WebCheckUtils.assertNotNull(id, "缺少参数id");
        return WebJsonBean.ok(questionService.deleteById(id));
    }

    @GetMapping("get_all_tags")
    public List<String> getAllTags() {
        return questionService.getAllTags();
    }

}
