package com.pugwoo.question.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 问题分类
 */
@Data
@ToString
@Table("question_category")
public class QuestionCategoryDO extends AdminCoreDO {

    /** 分类名称<br/>Column: [category_name] */
    @Column(value = "category_name")
    private String categoryName;

    /** 父节点的id，如果id为0，则是最顶级分类<br/>Column: [parent_id] */
    @Column(value = "parent_id")
    private Long parentId;

    /** 排序<br/>Column: [seq] */
    @Column(value = "seq")
    private Integer seq;

    /** 是否显示在菜单上<br/>Column: [is_menu] */
    @Column(value = "is_menu")
    private Boolean isMenu;

}