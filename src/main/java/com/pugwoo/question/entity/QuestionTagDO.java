package com.pugwoo.question.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 问题label
 */
@Data
@ToString
@Table("question_tag")
public class QuestionTagDO extends AdminCoreDO {

    /** 问题id<br/>Column: [question_id] */
    @Column(value = "question_id")
    private Long questionId;

    /** 标签<br/>Column: [tag] */
    @Column(value = "tag")
    private String tag;

}