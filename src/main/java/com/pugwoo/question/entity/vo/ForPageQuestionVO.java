package com.pugwoo.question.entity.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.question.entity.QuestionDO;
import com.pugwoo.question.entity.QuestionTagDO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

/**
 * 用于列表展示的QuestionDO
 */
@Data
public class ForPageQuestionVO extends QuestionDO {

    @JsonIgnore
    @RelatedColumn(localColumn = "creator_id", remoteColumn = "id")
    private AdminUserDO creatorDO;

    @JsonIgnore
    @RelatedColumn(localColumn = "updater_id", remoteColumn = "id")
    private AdminUserDO updaterDO;

    @JsonIgnore
    @RelatedColumn(localColumn = "category_id", remoteColumn = "id")
    private QuestionCategoryWithParentVO questionCategoryDO;

    @JsonIgnore
    @RelatedColumn(localColumn = "id", remoteColumn = "question_id")
    private List<QuestionTagDO> questionTags;

    /**前端用，勿删*/
    public String getCreatorName() {
        return creatorDO == null ? "" : creatorDO.getUserName();
    }

    /**前端用，勿删*/
    public String getUpdaterName() {
        return updaterDO == null ? "" : updaterDO.getUserName();
    }

    /**前端用，勿删*/
    public String getCategoryName() {
        return questionCategoryDO == null ? "" : questionCategoryDO.getCategoryName();
    }

    /**前端用，勿删*/
    public String getCategoryFullPath() {
        QuestionCategoryWithParentVO cur = questionCategoryDO;
        StringBuilder fullPath = new StringBuilder();
        while (cur != null) {
            fullPath.insert(0, "/" + cur.getCategoryName());
            cur = cur.getParent();
        }
        return fullPath.toString();
    }

    /**前端用，勿删*/
    public List<String> getTags() {
        return ListUtils.transform(questionTags, QuestionTagDO::getTag);
    }
}
