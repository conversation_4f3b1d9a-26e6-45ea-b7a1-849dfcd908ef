package com.pugwoo.question.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.question.entity.QuestionCategoryDO;
import lombok.Data;

import java.util.List;

/**
 * 问题分类节点树形结构定义
 */
@Data
public class QuestionCategoryTreeNodeVO extends QuestionCategoryDO {

    /**
     * 子节点列表
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @RelatedColumn(localColumn = "id", remoteColumn = "parent_id")
    private List<QuestionCategoryTreeNodeVO> children;

    /**
     * 该分类及其所有子孙分类的问题总数
     */
    private Integer questionCount;

}
