package com.pugwoo.question.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 问题修改记录
 */
@Data
@ToString
@Table("question_change_log")
public class QuestionChangeLogDO extends AdminCoreDO {

    /** 问题id<br/>Column: [question_id] */
    @Column(value = "question_id")
    private Long questionId;

    /** 编辑者id<br/>Column: [editor_id] */
    @Column(value = "editor_id")
    private Long editorId;

    @Column(value = "editor_name")
    private String editorName;

    /** 问题<br/>Column: [question] */
    @Column(value = "question")
    private String question;

    /** 答案<br/>Column: [answer] */
    @Column(value = "answer")
    private String answer;

    /** 答案html<br/>Column: [answer_html] */
    @Column(value = "answer_html")
    private String answerHtml;

    /** 更新说明<br/>Column: [note] */
    @Column(value = "note")
    private String note;

}