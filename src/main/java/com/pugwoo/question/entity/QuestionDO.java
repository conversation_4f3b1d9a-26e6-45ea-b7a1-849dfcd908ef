package com.pugwoo.question.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 问题
 */
@Data
@ToString
@Table("question")
public class QuestionDO extends AdminCoreDO {

    /** 创建人id<br/>Column: [creater_id] */
    @Column(value = "creator_id", insertValueScript = "com.pugwoo.question.utils.ForDBLoginUtils.getLoginUserId()")
    private Long creatorId;

    /** 更新人id<br/>Column: [updater_id] */
    @Column(value = "updater_id", insertValueScript = "com.pugwoo.question.utils.ForDBLoginUtils.getLoginUserId()",
            updateValueScript = "com.pugwoo.question.utils.ForDBLoginUtils.getLoginUserId()")
    private Long updaterId;

    /** 问题<br/>Column: [question] */
    @Column(value = "question")
    private String question;

    /** 答案<br/>Column: [answer] */
    @Column(value = "answer")
    private String answer;

    /** html格式的答案<br/>Column: [answer_html] */
    @Column(value = "answer_html")
    private String answerHtml;

    /** 回答的格式，例如markdown<br/>Column: [format] */
    @Column(value = "format")
    private String format;

    /** 分组id<br/>Column: [category_id] */
    @Column(value = "category_id")
    private Long categoryId;

    /** 排序<br/>Column: [seq] */
    @Column(value = "seq")
    private Integer seq;

    /** 星级，1-5星<br/>Column: [star] */
    @Column(value = "star")
    private Integer star;

}