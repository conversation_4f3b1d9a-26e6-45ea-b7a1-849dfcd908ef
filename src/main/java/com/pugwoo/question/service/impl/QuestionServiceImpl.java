package com.pugwoo.question.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.question.entity.QuestionChangeLogDO;
import com.pugwoo.question.entity.QuestionDO;
import com.pugwoo.question.entity.QuestionTagDO;
import com.pugwoo.question.entity.vo.ForPageQuestionVO;
import com.pugwoo.question.entity.vo.QuestionCategoryTreeNodeVO;
import com.pugwoo.question.service.QuestionCategoryService;
import com.pugwoo.question.service.QuestionService;
import com.pugwoo.question.utils.SomeUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class QuestionServiceImpl implements QuestionService {

    @Autowired
    private DBHelper dbHelper;
    @Resource
    private QuestionCategoryService questionCategoryService;

    @Override
    public QuestionDO getById(Integer id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(QuestionDO.class, id);
    }

    @Override
    public PageData<ForPageQuestionVO> getPage(int page, int pageSize, Long categoryId, List<Long> treeCheckedIds,
                                               String questionLike, List<String> tags) {
        WhereSQL whereSQL = new WhereSQL();
        if (ListUtils.isEmpty(treeCheckedIds)) {
            List<QuestionCategoryTreeNodeVO> categoryTree = questionCategoryService.getCategoryTree(categoryId);
            List<Long> categoryIds = SomeUtils.getCategoryTreeId(categoryTree);
            if (ListUtils.isNotEmpty(categoryIds)) {
                whereSQL.and("category_id in (?)", categoryIds);
            }
        } else {
            whereSQL.and("category_id in (?)", treeCheckedIds);
        }

        if (StringTools.isNotBlank(questionLike)) {
            String[] splits = questionLike.trim().split("\\s+");
            for (String split : splits) {
                whereSQL.and("question like ?", "%" + split + "%");
            }
        }

        if (ListUtils.isNotEmpty(tags)) {
            whereSQL.and("id in (select question_id from question_tag where deleted=0 and tag in (?))", tags);
        }

        return dbHelper.getPage(ForPageQuestionVO.class, page, pageSize, whereSQL);
    }

    @Override
    @Transactional
    public ResultBean<Long> insertOrUpdate(QuestionDO questionDO, List<String> tags) {
        if(questionDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }

        // 清理掉用户设置相关的值，由orm自动处理
        questionDO.setCreateTime(null);
        questionDO.setUpdateTime(null);
        questionDO.setCreatorId(null);
        questionDO.setUpdaterId(null);

        // 目前只有markdown
        questionDO.setFormat("markdown");

        // 只有改了问题或答案才记录change
        QuestionDO old = null;
        if (questionDO.getId() != null) {
            old = dbHelper.getByKey(QuestionDO.class, questionDO.getId());
        }

        boolean isInsert = questionDO.getId() == null;
        int rows = dbHelper.insertOrUpdate(questionDO);
        if (rows > 0 && (old == null || !Objects.equals(old.getQuestion(), questionDO.getQuestion())
            || !Objects.equals(old.getAnswer(), questionDO.getAnswer()))) { // 有改变才记录
            QuestionChangeLogDO changeLogDO = new QuestionChangeLogDO();
            changeLogDO.setQuestionId(questionDO.getId());
            AdminUserLoginContext loginContext = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
            if (loginContext != null) {
                changeLogDO.setEditorId(loginContext.getUserId());
                changeLogDO.setEditorName(loginContext.getUserName());
            }
            changeLogDO.setQuestion(questionDO.getQuestion());
            changeLogDO.setAnswer(questionDO.getAnswer());
            changeLogDO.setAnswerHtml(questionDO.getAnswerHtml());
            changeLogDO.setNote(old == null ? "新增" : ""); // TODO 后续可以引入AI自动处理变更摘要，这个可以异步进行
            dbHelper.insert(changeLogDO);
        }

        // 处理tags的保存
        tags = ListUtils.filter(tags, StringTools::isNotBlank);
        if (isInsert) {
            if (ListUtils.isNotEmpty(tags)) {
                List<QuestionTagDO> tagDOs = ListUtils.transform(tags, tag -> {
                    QuestionTagDO questionTagDO = new QuestionTagDO();
                    questionTagDO.setQuestionId(questionDO.getId());
                    questionTagDO.setTag(tag);
                    return questionTagDO;
                });
                dbHelper.insertBatchWithoutReturnId(tagDOs);
            }
        } else {
            List<QuestionTagDO> dbTags = dbHelper.getAll(QuestionTagDO.class,
                    "where question_id=?", questionDO.getId());
            List<String> finalTags = tags;
            List<QuestionTagDO> toDelete = ListUtils.filter(dbTags, o -> !finalTags.contains(o.getTag()));
            dbHelper.delete(toDelete);
            List<String> toInsert = ListUtils.filter(tags, tag -> {
                for (QuestionTagDO dbTag : dbTags) {
                    if (Objects.equals(tag, dbTag.getTag())) {
                        return false;
                    }
                }
                return true;
            });
            List<QuestionTagDO> tagDOs = ListUtils.transform(toInsert, tag -> {
                QuestionTagDO questionTagDO = new QuestionTagDO();
                questionTagDO.setQuestionId(questionDO.getId());
                questionTagDO.setTag(tag);
                return questionTagDO;
            });
            dbHelper.insertBatchWithoutReturnId(tagDOs);
        }

        return rows > 0 ? ResultBean.ok(questionDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        dbHelper.delete(QuestionTagDO.class, "where question_id=?", id);

        QuestionDO questionDO = new QuestionDO();
        questionDO.setId(id);
        return dbHelper.delete(questionDO) > 0;
    }

    @Override
    public List<String> getAllTags() {
        return dbHelper.getRaw(String.class,
                "select distinct tag from question_tag where deleted=0");
    }
}