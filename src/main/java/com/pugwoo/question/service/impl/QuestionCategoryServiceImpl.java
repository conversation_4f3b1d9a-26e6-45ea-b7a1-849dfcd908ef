package com.pugwoo.question.service.impl;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.question.entity.QuestionCategoryDO;
import com.pugwoo.question.entity.vo.QuestionCategoryTreeNodeVO;
import com.pugwoo.question.service.QuestionCategoryService;
import com.pugwoo.question.utils.SomeUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class QuestionCategoryServiceImpl implements QuestionCategoryService {

    @Autowired
    private DBHelper dbHelper;

    @Override
    public QuestionCategoryDO getById(Long id) {
        return id == null ? null : dbHelper.getByKey(QuestionCategoryDO.class, id);
    }

    @Override
    public List<QuestionCategoryTreeNodeVO> getCategoryTree(Long categoryId) {
        List<QuestionCategoryTreeNodeVO> categories;
        if (categoryId == null || categoryId == 0L) {
            categories = dbHelper.getAll(QuestionCategoryTreeNodeVO.class, "where parent_id=0");
        } else {
            categories = dbHelper.getAll(QuestionCategoryTreeNodeVO.class,
                    "where id=?", categoryId);
        }

        // 处理节点内排序
        sort(categories);

        // 计算并设置每个节点的问题数量
        calculateQuestionCounts(categories);

        return categories;
    }

    private void sort(List<QuestionCategoryTreeNodeVO> categories) {
        if (categories == null || categories.isEmpty()) {
            return;
        }
        ListUtils.sortAscNullFirst(categories, QuestionCategoryDO::getSeq);
        for (QuestionCategoryTreeNodeVO category : categories) {
            if (category.getChildren() == null || category.getChildren().isEmpty()) {
                continue;
            }
            sort(category.getChildren());
        }
    }

    private void setSeq(List<QuestionCategoryTreeNodeVO> tree) {
        if (tree == null || tree.isEmpty()) {
            return;
        }
        int seq = 1;
        for (QuestionCategoryTreeNodeVO category : tree) {
            category.setSeq(seq++);
            if (category.getChildren() == null || category.getChildren().isEmpty()) {
                continue;
            }
            setSeq(category.getChildren());
        }
    }

    @Override
    @Synchronized(customExceptionMessage = "当前有其它请求并发保存分类，请稍后操作")
    @Transactional
    public void saveCategoryTree(Long categoryId, @NotNull List<QuestionCategoryTreeNodeVO> tree) {
        // 1. 准备好老的和新的category
        List<QuestionCategoryTreeNodeVO> dbCategories = getCategoryTree(categoryId);
        List<Long> dbCategoryIds = SomeUtils.getCategoryTreeId(dbCategories);

        setSeq(tree);
        List<Long> newCategoryIds = SomeUtils.getCategoryTreeId(tree);

        // 2. 检查一下这种情况：如果新的id在老的id里没有，那么有问题
        if (!ListUtils.filter(newCategoryIds, o -> !dbCategoryIds.contains(o)).isEmpty()) {
            throw new RuntimeException("参数有误：提交上来的分类id在数据库中不存在，可能分类数据已被更新，请刷新重试");
        }

        // 3. 比较得出需要删除的category
        List<Long> toDeleteId = ListUtils.filter(dbCategoryIds,
               o -> !newCategoryIds.contains(o));
        dbHelper.delete(ListUtils.transform(toDeleteId, o -> {
            QuestionCategoryDO categoryDO = new QuestionCategoryDO();
            categoryDO.setId(o);
            return categoryDO;
        }));

        // 4. 递归插入和更新分组
        List<QuestionCategoryTreeNodeVO> toUpdate = new ArrayList<>();
        long defaultParentId = categoryId == null || categoryId == 0L ?
                0L : (tree.size() == 1 ? tree.getFirst().getParentId() : 0L);
        saveOrUpdate(tree, defaultParentId, toUpdate);
        dbHelper.update(toUpdate);
    }

    /**特别说明：返回的是需要update的，为什么insert需要及时插入：因为其子节点的parent需要插入之后才有值*/
    private void saveOrUpdate(@NotNull List<QuestionCategoryTreeNodeVO> tree,
                              Long parentId, List<QuestionCategoryTreeNodeVO> toUpdate) {
        for (QuestionCategoryTreeNodeVO node : tree) {
            node.setParentId(parentId);
            if (node.getId() == null) {
                dbHelper.insert(node);
            } else {
                toUpdate.add(node);
            }
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                saveOrUpdate(node.getChildren(), node.getId(), toUpdate);
            }
        }
    }

    @Override
    public Map<Long, Integer> getQuestionCountByCategoryIds(List<Long> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Map> results = dbHelper.getRaw(Map.class,
                "select category_id, count(*) as question_count from question where deleted=0 and category_id in (?) group by category_id",
                categoryIds);

        Map<Long, Integer> countMap = new HashMap<>();
        for (Map<String, Object> result : results) {
            Long categoryId = (Long) result.get("category_id");
            Number count = (Number) result.get("question_count");
            countMap.put(categoryId, count.intValue());
        }

        return countMap;
    }

    /**
     * 计算并设置树中每个节点的问题数量（包括子孙节点的问题数量）
     */
    private void calculateQuestionCounts(List<QuestionCategoryTreeNodeVO> categories) {
        if (categories == null || categories.isEmpty()) {
            return;
        }

        // 收集所有分类ID
        List<Long> allCategoryIds = SomeUtils.getCategoryTreeId(categories);

        // 批量查询每个分类的问题数量
        Map<Long, Integer> questionCountMap = getQuestionCountByCategoryIds(allCategoryIds);

        // 递归设置每个节点的问题数量
        setQuestionCounts(categories, questionCountMap);
    }

    /**
     * 递归设置每个节点的问题数量
     */
    private int setQuestionCounts(List<QuestionCategoryTreeNodeVO> categories, Map<Long, Integer> questionCountMap) {
        int totalCount = 0;

        for (QuestionCategoryTreeNodeVO category : categories) {
            int currentCount = 0;

            // 获取当前分类的直接问题数量
            Integer directCount = questionCountMap.get(category.getId());
            if (directCount != null) {
                currentCount += directCount;
            }

            // 递归计算子节点的问题数量
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                currentCount += setQuestionCounts(category.getChildren(), questionCountMap);
            }

            // 设置当前节点的问题数量
            category.setQuestionCount(currentCount);
            totalCount += currentCount;
        }

        return totalCount;
    }

}
