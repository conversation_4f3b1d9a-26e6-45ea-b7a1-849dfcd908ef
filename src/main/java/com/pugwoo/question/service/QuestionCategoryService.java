package com.pugwoo.question.service;


import com.pugwoo.question.entity.QuestionCategoryDO;
import com.pugwoo.question.entity.vo.QuestionCategoryTreeNodeVO;

import java.util.List;

public interface QuestionCategoryService {

    QuestionCategoryDO getById(Long id);

    /**
     * 查询分类，返回树型结构，支持指定哪些分类id及其所有子分类，一次性全部查回来
     * 返回的数据不包含categoryId本身，设置为0表示查询根目录
     */
    List<QuestionCategoryTreeNodeVO> getCategoryTree(Long categoryId);

    /**
     * 保存分类树型结构，支持局部更新
     * @param categoryId 要保存的树型范围，这个对应于数据库的范围
     * @param tree 要保存的树型结构，必须在范围内的全量过来
     * @return 如果不为空，则为warning信息
     */
    void saveCategoryTree(Long categoryId, List<QuestionCategoryTreeNodeVO> tree);

    /**
     * 统计指定分类ID列表中每个分类的问题数量
     * @param categoryIds 分类ID列表
     * @return 分类ID到问题数量的映射
     */
    java.util.Map<Long, Integer> getQuestionCountByCategoryIds(List<Long> categoryIds);

}
