package com.pugwoo.question.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.question.entity.QuestionDO;
import com.pugwoo.question.entity.vo.ForPageQuestionVO;

import java.util.List;

public interface QuestionService {

    /**
     * 通过主键获得数据
     */
    QuestionDO getById(Integer id);
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param categoryId 查询指定分类下的所有问题，包括该categoryId
     * @param treeCheckedIds 当这个有值时，优先用这个，忽略categoryId
     * @param questionLike 支持问题模糊搜索，空格隔开
     * @param tags 支持tags搜索，tags是且的关系
     */
    PageData<ForPageQuestionVO> getPage(int page, int pageSize, Long categoryId, List<Long> treeCheckedIds,
                                        String questionLike, List<String> tags);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(QuestionDO questionDO, List<String> tags);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    /**
     * 查询所有的tags
     */
    List<String> getAllTags();

}