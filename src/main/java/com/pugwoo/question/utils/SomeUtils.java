package com.pugwoo.question.utils;

import com.pugwoo.question.entity.vo.QuestionCategoryTreeNodeVO;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;

import java.util.ArrayList;
import java.util.List;

public class SomeUtils {

    public static List<Long> parseCategoryIds(String categoryIds) {
        if (StringTools.isBlank(categoryIds)) {
            return null;
        }
        String[] strings = categoryIds.split(",");
        List<Long> categoryIdList = new ArrayList<>();
        for (String str : strings) {
            Long l = NumberUtils.parseLong(str);
            if (l != null) {
                categoryIdList.add(l);
            }
        }
        return categoryIdList;
    }

    public static List<String> parseTags(String tags) {
        if (StringTools.isBlank(tags)) {
            return null;
        }
        String[] strings = tags.split(",");
        return ListUtils.transform(ListUtils.filter(strings, StringTools::isNotBlank), String::trim);
    }

    public static List<Long> getCategoryTreeId(List<QuestionCategoryTreeNodeVO> tree) {
        List<Long> ids = new ArrayList<>();
        if (tree == null) {
            return ids;
        }
        for (QuestionCategoryTreeNodeVO node : tree) {
            if (node.getId() == null) {
                continue;
            }
            ids.add(node.getId());
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                ids.addAll(getCategoryTreeId(node.getChildren()));
            }
        }
        return ids;
    }

}
